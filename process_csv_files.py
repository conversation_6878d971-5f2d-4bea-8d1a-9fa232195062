import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import os
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def extract_four_bytes_to_signed_int32(data_str):
    """从数据字符串中提取后四位字节并转换为有符号32位整数
    
    处理逻辑：
    1. 提取后四位字节，例如：32 12 07 7F CC FD FF -> 7F CC FD FF
    2. 第一个数据是低位，最后一个是高位，重新排列：7F CC FD FF -> FF FD CC 7F
    3. 转换为有符号32位整数
    """
    try:
        if pd.isna(data_str) or not data_str:
            return None
        # 移除可能的前缀和多余空格
        data_str = str(data_str).replace('x|', '').strip()
        # 分割十六进制数据
        hex_bytes = data_str.split()
        
        if len(hex_bytes) >= 4:
            # 取后四个字节
            last_four = hex_bytes[-4:]
            
            # 第一个是低位，最后一个是高位，所以要反转顺序
            reversed_bytes = last_four[::-1]
            hex_value = ''.join(reversed_bytes)
            
            # 转换为无符号整数
            unsigned_int = int(hex_value, 16)
            
            # 转换为有符号32位整数
            if unsigned_int >= 2**31:
                signed_int = unsigned_int - 2**32
            else:
                signed_int = unsigned_int
            
            return signed_int
        return None
    except Exception as e:
        return None

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']
    
    for encoding in encodings:
        try:
            print(f"尝试使用 {encoding} 编码读取 {file_path}")
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取文件")
            return df
        except Exception as e:
            print(f"使用 {encoding} 编码失败: {e}")
            continue
    
    print(f"无法读取文件 {file_path}")
    return None

def process_csv_file(file_path, target_ids):
    """处理单个CSV文件"""
    print(f"\n{'='*60}")
    print(f"正在处理文件: {file_path}")
    
    # 读取CSV文件
    df = read_csv_with_encoding(file_path)
    if df is None:
        return {}
    
    print(f"文件形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print("前5行数据:")
    print(df.head())
    
    # 查找ID列和数据列
    id_column = None
    data_column = None
    time_column = None
    
    for col in df.columns:
        col_str = str(col).upper()
        if 'ID' in col_str or '号' in col_str:
            id_column = col
        elif '数据' in col_str or 'DATA' in col_str:
            data_column = col
        elif '时间' in col_str or 'TIME' in col_str:
            time_column = col
    
    print(f"识别的列: ID列={id_column}, 数据列={data_column}, 时间列={time_column}")
    
    if id_column is None or data_column is None:
        print("无法识别ID列或数据列")
        return {}
    
    results = {}
    
    # 处理每个目标ID
    for target_id in target_ids:
        print(f"\n处理ID: {target_id}")
        
        # 筛选目标ID的数据
        mask = df[id_column] == target_id
        target_data = df[mask]
        
        if len(target_data) == 0:
            print(f"未找到ID {target_id} 的数据")
            continue
        
        print(f"找到 {len(target_data)} 个 {target_id} 的数据点")
        
        # 提取并转换数据
        converted_values = []
        timestamps = []
        
        for idx, row in target_data.iterrows():
            data_value = row[data_column]
            time_value = row[time_column] if time_column else f"row_{idx}"
            
            converted_value = extract_four_bytes_to_signed_int32(data_value)
            if converted_value is not None:
                converted_values.append(converted_value)
                timestamps.append(str(time_value))
        
        if len(converted_values) > 0:
            results[target_id] = {
                'values': converted_values,
                'timestamps': timestamps
            }
            print(f"成功转换 {len(converted_values)} 个数据点")
            print(f"前5个值: {converted_values[:5]}")
        else:
            print(f"ID {target_id} 没有有效的转换数据")
    
    return results

def plot_and_save_data(file_name, all_results, target_ids):
    """绘制图表并保存数据"""
    if not all_results:
        print("没有数据可以绘制")
        return
    
    # 创建大图表显示所有ID的数据
    fig, axes = plt.subplots(3, 4, figsize=(24, 18))
    fig.suptitle(f'{file_name} - 12个CAN ID数据折线图', fontsize=16)
    
    colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown', 
              'pink', 'gray', 'olive', 'cyan', 'magenta', 'yellow']
    
    for i, target_id in enumerate(target_ids):
        row = i // 4
        col = i % 4
        ax = axes[row, col]
        
        if target_id in all_results:
            values = all_results[target_id]['values']
            ax.plot(range(len(values)), values, color=colors[i], linewidth=0.8, alpha=0.8)
            ax.set_title(f'ID {target_id} ({len(values)} 点)', fontsize=10)
            
            # 添加统计信息
            if len(values) > 0:
                mean_val = np.mean(values)
                ax.axhline(y=mean_val, color='black', linestyle='--', alpha=0.5)
                ax.text(0.02, 0.98, f'均值: {mean_val:.0f}', transform=ax.transAxes, 
                       verticalalignment='top', fontsize=8)
        else:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f'ID {target_id} (无数据)', fontsize=10)
        
        ax.set_xlabel('数据点序号', fontsize=8)
        ax.set_ylabel('有符号32位整数值', fontsize=8)
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    plot_filename = f'{file_name}_all_ids_plot.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"图表已保存为 '{plot_filename}'")
    
    # 保存数据到CSV
    csv_filename = f'{file_name}_processed_data.csv'
    with open(csv_filename, 'w', encoding='utf-8') as f:
        f.write("ID,时间,有符号32位整数值\n")
        for target_id in target_ids:
            if target_id in all_results:
                values = all_results[target_id]['values']
                timestamps = all_results[target_id]['timestamps']
                for time, value in zip(timestamps, values):
                    f.write(f"{target_id},{time},{value}\n")
    
    print(f"数据已保存为 '{csv_filename}'")

# 主程序
if __name__ == "__main__":
    # 4个CSV文件
    csv_files = [
        '0857_0001.csv',
        '0900_0001.csv', 
        '0901_0002.csv',
        '0905_0004.csv'
    ]
    
    # 12个目标ID
    target_ids = ['0x0181', '0x0182', '0x0183', '0x0184', '0x0185', '0x0186', 
                  '0x0187', '0x0188', '0x018A', '0x018B', '0x018C']
    
    print("开始处理4个CSV文件的12个CAN ID数据...")
    print("数据处理逻辑：")
    print("1. 提取后四位字节")
    print("2. 第一个字节是低位，最后一个字节是高位")
    print("3. 重新排列后转换为有符号32位整数")
    print()
    
    # 处理每个CSV文件
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            file_name = os.path.splitext(csv_file)[0]
            results = process_csv_file(csv_file, target_ids)
            plot_and_save_data(file_name, results, target_ids)
        else:
            print(f"文件不存在: {csv_file}")
    
    print(f"\n{'='*60}")
    print("所有CSV文件处理完成！")
    print("每个文件生成了：")
    print("- 1个包含12个ID的综合折线图")
    print("- 1个包含所有处理数据的CSV文件")
