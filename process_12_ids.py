import zipfile
import xml.etree.ElementTree as ET
import re
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def extract_four_bytes_to_signed_int32(data_str):
    """从数据字符串中提取后四位字节并转换为有符号32位整数
    
    处理逻辑：
    1. 提取后四位字节，例如：32 12 07 7F CC FD FF -> 7F CC FD FF
    2. 第一个数据是低位，最后一个是高位，重新排列：7F CC FD FF -> FF FD CC 7F
    3. 转换为有符号32位整数
    """
    try:
        if not data_str:
            return [None, None, None, None]
        # 移除 'x|' 前缀和多余空格
        data_str = str(data_str).replace('x|', '').strip()
        # 分割十六进制数据
        hex_bytes = data_str.split()
        
        if len(hex_bytes) >= 4:
            # 取后四个字节
            last_four = hex_bytes[-4:]
            
            # 每个字节单独处理，第一个是低位，最后一个是高位
            # 所以要反转顺序组成32位数据
            reversed_bytes = last_four[::-1]
            hex_value = ''.join(reversed_bytes)
            
            # 转换为无符号整数
            unsigned_int = int(hex_value, 16)
            
            # 转换为有符号32位整数
            if unsigned_int >= 2**31:
                signed_int = unsigned_int - 2**32
            else:
                signed_int = unsigned_int
            
            # 同时返回4个独立的字节值（转换为有符号整数）
            byte_values = []
            for i, byte_str in enumerate(last_four):
                byte_val = int(byte_str, 16)
                # 将字节值转换为有符号8位整数
                if byte_val >= 128:
                    byte_val = byte_val - 256
                byte_values.append(byte_val)
            
            return [signed_int] + byte_values
        return [None, None, None, None, None]
    except Exception as e:
        return [None, None, None, None, None]

def process_can_id(target_id):
    """处理指定ID的CAN数据"""
    file_path = '/Users/<USER>/Desktop/数据处理/0903_0003.xlsx'
    print(f"正在处理ID {target_id} 的数据...")
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            try:
                with zip_file.open('xl/sharedStrings.xml') as f:
                    tree = ET.parse(f)
                    root = tree.getroot()
                    for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                        t = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                        if t is not None:
                            shared_strings.append(t.text)
                        else:
                            shared_strings.append('')
            except:
                print("无法读取共享字符串，将直接解析单元格值")
            
            # 读取工作表数据
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                tree = ET.parse(f)
                root = tree.getroot()
                
                # 存储数据 - 32位组合值和4个独立字节值
                combined_values = []  # 32位组合值
                byte1_values = []     # 第1个字节
                byte2_values = []     # 第2个字节
                byte3_values = []     # 第3个字节
                byte4_values = []     # 第4个字节
                timestamps = []
                row_count = 0
                
                # 解析每一行
                for row in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row'):
                    row_count += 1
                    if row_count == 1:  # 跳过标题行
                        continue
                    
                    if row_count % 10000 == 0:
                        print(f"已处理 {row_count} 行，找到 {len(combined_values)} 个{target_id}数据点")
                    
                    cells = row.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c')
                    
                    # 提取需要的列数据
                    id_value = None
                    data_value = None
                    time_value = None
                    
                    for cell in cells:
                        r = cell.get('r')  # 单元格引用，如A1, B1等
                        if r:
                            col_letter = re.match(r'([A-Z]+)', r).group(1)
                            
                            # 获取单元格值
                            v = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                            if v is not None:
                                cell_value = v.text
                                
                                # 如果是共享字符串类型
                                t = cell.get('t')
                                if t == 's' and cell_value and shared_strings:
                                    try:
                                        cell_value = shared_strings[int(cell_value)]
                                    except:
                                        pass
                                
                                # 根据列位置提取数据
                                if col_letter == 'F':  # ID号列
                                    id_value = cell_value
                                elif col_letter == 'J':  # 数据列
                                    data_value = cell_value
                                elif col_letter == 'B':  # 时间列
                                    time_value = cell_value
                    
                    # 处理目标ID的数据
                    if id_value == target_id and data_value:
                        values = extract_four_bytes_to_signed_int32(data_value)
                        if values[0] is not None:
                            combined_values.append(values[0])
                            byte1_values.append(values[1])
                            byte2_values.append(values[2])
                            byte3_values.append(values[3])
                            byte4_values.append(values[4])
                            timestamps.append(str(time_value) if time_value else f"row_{row_count}")
        
        return {
            'combined': combined_values,
            'byte1': byte1_values,
            'byte2': byte2_values,
            'byte3': byte3_values,
            'byte4': byte4_values,
            'timestamps': timestamps
        }
    
    except Exception as e:
        print(f"处理数据时出错: {e}")
        return {
            'combined': [],
            'byte1': [],
            'byte2': [],
            'byte3': [],
            'byte4': [],
            'timestamps': []
        }

def plot_and_save_data(target_id, data_dict):
    """绘制图表并保存数据"""
    combined_values = data_dict['combined']
    timestamps = data_dict['timestamps']
    
    if len(combined_values) == 0:
        print(f"未找到ID为{target_id}的数据")
        return
    
    print(f"\n总共找到 {len(combined_values)} 个ID为{target_id}的数据点")
    print(f"前10个32位组合值: {combined_values[:10]}")
    print(f"32位组合值范围: {min(combined_values)} 到 {max(combined_values)}")
    
    # 绘制32位组合值的折线图
    plt.figure(figsize=(15, 8))
    plt.plot(range(len(combined_values)), combined_values, 'b-', linewidth=0.8, alpha=0.8)
    plt.title(f'ID {target_id} 32位组合数据折线图', fontsize=14)
    plt.xlabel('数据点序号', fontsize=12)
    plt.ylabel('有符号32位整数值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    mean_val = np.mean(combined_values)
    std_val = np.std(combined_values)
    plt.axhline(y=mean_val, color='r', linestyle='--', alpha=0.7, label=f'平均值: {mean_val:.2f}')
    plt.axhline(y=mean_val + std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值+标准差: {mean_val + std_val:.2f}')
    plt.axhline(y=mean_val - std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值-标准差: {mean_val - std_val:.2f}')
    
    plt.legend()
    plt.tight_layout()
    
    # 保存32位组合值图表
    plot_filename = f'{target_id}_combined_data_plot.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n32位组合值统计信息:")
    print(f"平均值: {mean_val:.2f}")
    print(f"标准差: {std_val:.2f}")
    print(f"最小值: {min(combined_values)}")
    print(f"最大值: {max(combined_values)}")
    print(f"32位组合值图表已保存为 '{plot_filename}'")
    
    # 绘制4个字节的独立折线图
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle(f'ID {target_id} 四个字节独立数据折线图', fontsize=16)
    
    byte_data = [data_dict['byte1'], data_dict['byte2'], data_dict['byte3'], data_dict['byte4']]
    byte_names = ['第1字节(低位)', '第2字节', '第3字节', '第4字节(高位)']
    colors = ['red', 'green', 'blue', 'orange']
    
    for i, (ax, data, name, color) in enumerate(zip(axes.flat, byte_data, byte_names, colors)):
        ax.plot(range(len(data)), data, color=color, linewidth=0.8, alpha=0.8)
        ax.set_title(f'{name}', fontsize=12)
        ax.set_xlabel('数据点序号', fontsize=10)
        ax.set_ylabel('有符号8位整数值', fontsize=10)
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        if len(data) > 0:
            mean_val = np.mean(data)
            ax.axhline(y=mean_val, color='black', linestyle='--', alpha=0.5, label=f'平均值: {mean_val:.1f}')
            ax.legend()
    
    plt.tight_layout()
    
    # 保存4字节图表
    bytes_plot_filename = f'{target_id}_bytes_data_plot.png'
    plt.savefig(bytes_plot_filename, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"4字节独立图表已保存为 '{bytes_plot_filename}'")
    
    # 保存数据到CSV文件
    data_filename = f'{target_id}_all_data.csv'
    with open(data_filename, 'w') as f:
        f.write("时间,32位组合值,第1字节,第2字节,第3字节,第4字节\n")
        for i in range(len(combined_values)):
            f.write(f"{timestamps[i]},{combined_values[i]},{byte_data[0][i]},{byte_data[1][i]},{byte_data[2][i]},{byte_data[3][i]}\n")
    print(f"所有数据已保存为 '{data_filename}'")

# 主程序
if __name__ == "__main__":
    # 要处理的12个ID列表
    target_ids = ['0x0181', '0x0182', '0x0183', '0x0184', '0x0185', '0x0186', 
                  '0x0187', '0x0188', '0x018A', '0x018B', '0x018C']
    
    print("开始处理12个CAN ID的数据...")
    print("数据处理逻辑：")
    print("1. 提取后四位字节")
    print("2. 第一个字节是低位，最后一个字节是高位")
    print("3. 重新排列后转换为有符号32位整数")
    print("4. 同时提取4个独立字节值")
    print()
    
    for target_id in target_ids:
        print(f"\n{'='*60}")
        data_dict = process_can_id(target_id)
        plot_and_save_data(target_id, data_dict)
    
    print(f"\n{'='*60}")
    print("所有12个ID处理完成！")
    print("每个ID生成了：")
    print("- 1个32位组合值折线图")
    print("- 1个4字节独立折线图")
    print("- 1个包含所有数据的CSV文件")
