import openpyxl
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import struct
import re

def hex_to_signed_int32(hex_str):
    """将十六进制字符串转换为有符号32位整数"""
    try:
        # 移除可能的前缀和空格
        hex_str = hex_str.replace('0x', '').replace(' ', '')
        # 转换为无符号整数
        unsigned_int = int(hex_str, 16)
        # 转换为有符号32位整数
        if unsigned_int >= 2**31:
            signed_int = unsigned_int - 2**32
        else:
            signed_int = unsigned_int
        return signed_int
    except:
        return None

def extract_last_four_bytes(data_str):
    """从数据字符串中提取后四位（字节）"""
    try:
        # 移除 'x|' 前缀和多余空格
        data_str = data_str.replace('x|', '').strip()
        # 分割十六进制数据
        hex_bytes = data_str.split()
        
        if len(hex_bytes) >= 4:
            # 取后四个字节
            last_four = hex_bytes[-4:]
            # 组合成一个32位的十六进制字符串（小端序）
            hex_value = ''.join(reversed(last_four))
            return hex_value
        return None
    except:
        return None

# 读取Excel文件
file_path = '0903_0003.xlsx'
print("正在处理CAN数据...")

try:
    workbook = openpyxl.load_workbook(file_path, data_only=True, read_only=True)
    worksheet = workbook.active
    
    # 存储0x0181的数据
    data_0x0181 = []
    timestamps = []
    
    max_row = worksheet.max_row
    print(f"总行数: {max_row}")
    
    # 遍历所有行，查找ID号为0x0181的数据
    for row in range(2, max_row + 1):  # 从第2行开始（跳过标题行）
        id_cell = worksheet.cell(row=row, column=6).value  # ID号在第6列
        data_cell = worksheet.cell(row=row, column=10).value  # 数据在第10列
        time_cell = worksheet.cell(row=row, column=2).value  # 时间在第2列
        
        if id_cell == '0x0181' and data_cell:
            # 提取后四位数据
            last_four_hex = extract_last_four_bytes(str(data_cell))
            if last_four_hex:
                # 转换为有符号32位整数
                signed_value = hex_to_signed_int32(last_four_hex)
                if signed_value is not None:
                    data_0x0181.append(signed_value)
                    timestamps.append(str(time_cell))
        
        # 每处理1000行显示进度
        if row % 1000 == 0:
            print(f"已处理 {row} 行，找到 {len(data_0x0181)} 个0x0181数据点")
    
    workbook.close()
    
    print(f"\n总共找到 {len(data_0x0181)} 个ID为0x0181的数据点")
    
    if len(data_0x0181) > 0:
        print(f"前10个数据值: {data_0x0181[:10]}")
        print(f"数据范围: {min(data_0x0181)} 到 {max(data_0x0181)}")
        
        # 绘制折线图
        plt.figure(figsize=(12, 6))
        plt.plot(range(len(data_0x0181)), data_0x0181, 'b-', linewidth=1)
        plt.title('ID 0x0181 数据的后四位转换为有符号32位整数的折线图')
        plt.xlabel('数据点序号')
        plt.ylabel('有符号32位整数值')
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_val = np.mean(data_0x0181)
        std_val = np.std(data_0x0181)
        plt.axhline(y=mean_val, color='r', linestyle='--', alpha=0.7, label=f'平均值: {mean_val:.2f}')
        plt.axhline(y=mean_val + std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值+标准差: {mean_val + std_val:.2f}')
        plt.axhline(y=mean_val - std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值-标准差: {mean_val - std_val:.2f}')
        
        plt.legend()
        plt.tight_layout()
        plt.savefig('0x0181_data_plot.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n统计信息:")
        print(f"平均值: {mean_val:.2f}")
        print(f"标准差: {std_val:.2f}")
        print(f"最小值: {min(data_0x0181)}")
        print(f"最大值: {max(data_0x0181)}")
        print(f"图表已保存为 '0x0181_data_plot.png'")
        
    else:
        print("未找到ID为0x0181的数据")

except Exception as e:
    print(f"处理数据时出错: {e}")
    import traceback
    traceback.print_exc()
