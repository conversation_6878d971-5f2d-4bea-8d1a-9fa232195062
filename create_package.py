import os
import zipfile
from datetime import datetime

def create_package():
    """创建包含所有相关文件的压缩包"""
    
    # 获取当前时间作为包名的一部分
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"CAN数据处理项目_{timestamp}.zip"
    
    # 定义要打包的文件列表
    files_to_package = [
        # README文件
        "README.md",
        
        # 原始数据
        "0903_0003.xlsx",
        
        # 主要处理脚本
        "parse_xlsx_xml.py",
        "process_multiple_ids.py",
        "convert_txt_to_csv.py",
        
        # 处理后的CSV数据文件
        "0x0181_processed_data.csv",
        "0x0182_processed_data.csv",
        "0x0183_processed_data.csv",
        "0x0184_processed_data.csv",
        "0x0185_processed_data.csv",
        "0x0186_processed_data.csv",
        
        # 可视化图表
        "0x0181_data_plot.png",
        "0x0182_data_plot.png",
        "0x0183_data_plot.png",
        "0x0184_data_plot.png",
        "0x0185_data_plot.png",
        "0x0186_data_plot.png",
    ]
    
    # 创建压缩包
    with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        print(f"正在创建压缩包: {package_name}")
        
        # 创建文件夹结构
        folders = {
            "原始数据/": ["0903_0003.xlsx"],
            "处理脚本/": ["parse_xlsx_xml.py", "process_multiple_ids.py", "convert_txt_to_csv.py"],
            "处理结果/CSV数据/": [f for f in files_to_package if f.endswith('.csv')],
            "处理结果/可视化图表/": [f for f in files_to_package if f.endswith('.png')],
        }
        
        # 添加README到根目录
        if os.path.exists("README.md"):
            zipf.write("README.md", "README.md")
            print("✓ 已添加: README.md")
        
        # 按文件夹结构添加文件
        for folder, file_list in folders.items():
            for filename in file_list:
                if os.path.exists(filename):
                    archive_path = folder + filename
                    zipf.write(filename, archive_path)
                    print(f"✓ 已添加: {archive_path}")
                else:
                    print(f"✗ 文件不存在: {filename}")
        
        # 添加项目结构说明
        project_structure = """CAN数据处理项目文件结构

├── README.md                           # 项目说明文档
├── 原始数据/
│   └── 0903_0003.xlsx                 # 原始CAN总线数据
├── 处理脚本/
│   ├── parse_xlsx_xml.py              # 单个ID处理脚本
│   ├── process_multiple_ids.py        # 多ID处理脚本
│   └── convert_txt_to_csv.py          # 格式转换工具
├── 处理结果/
│   ├── CSV数据/
│   │   ├── 0x0181_processed_data.csv  # ID 0x0181 数据
│   │   ├── 0x0182_processed_data.csv  # ID 0x0182 数据
│   │   ├── 0x0183_processed_data.csv  # ID 0x0183 数据
│   │   ├── 0x0184_processed_data.csv  # ID 0x0184 数据
│   │   ├── 0x0185_processed_data.csv  # ID 0x0185 数据
│   │   └── 0x0186_processed_data.csv  # ID 0x0186 数据
│   └── 可视化图表/
│       ├── 0x0181_data_plot.png       # ID 0x0181 折线图
│       ├── 0x0182_data_plot.png       # ID 0x0182 折线图
│       ├── 0x0183_data_plot.png       # ID 0x0183 折线图
│       ├── 0x0184_data_plot.png       # ID 0x0184 折线图
│       ├── 0x0185_data_plot.png       # ID 0x0185 折线图
│       └── 0x0186_data_plot.png       # ID 0x0186 折线图

使用说明：
1. 查看 README.md 了解详细的项目说明
2. 运行处理脚本需要安装依赖：pip install pandas numpy matplotlib openpyxl
3. CSV文件可直接用Excel或其他数据分析工具打开
4. PNG图表文件可直接查看数据趋势
"""
        
        zipf.writestr("项目结构说明.txt", project_structure)
        print("✓ 已添加: 项目结构说明.txt")
    
    print(f"\n✅ 打包完成！")
    print(f"📦 压缩包名称: {package_name}")
    print(f"📁 压缩包大小: {os.path.getsize(package_name) / 1024 / 1024:.2f} MB")
    
    return package_name

if __name__ == "__main__":
    package_name = create_package()
    print(f"\n🎉 所有文件已成功打包到: {package_name}")
