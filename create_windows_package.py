import os
import zipfile
from datetime import datetime
import shutil

def create_windows_compatible_package():
    """创建Windows兼容的压缩包，使用英文文件名和路径"""
    
    # 获取当前时间作为包名的一部分
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"CAN_Data_Processing_Project_{timestamp}.zip"
    
    # 创建临时文件夹结构（使用英文名）
    temp_dir = "temp_package"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    
    # 创建文件夹结构
    folders = {
        "original_data": "原始数据",
        "scripts": "处理脚本", 
        "results/csv_data": "处理结果/CSV数据",
        "results/charts": "处理结果/图表"
    }
    
    for folder in folders.keys():
        os.makedirs(os.path.join(temp_dir, folder), exist_ok=True)
    
    # 文件映射（原文件名 -> 新文件名）
    file_mappings = {
        # 原始数据
        "0903_0003.xlsx": "original_data/can_data_0903_0003.xlsx",
        
        # 脚本文件
        "parse_xlsx_xml.py": "scripts/parse_xlsx_xml.py",
        "process_multiple_ids.py": "scripts/process_multiple_ids.py", 
        "convert_txt_to_csv.py": "scripts/convert_txt_to_csv.py",
        
        # CSV数据文件
        "0x0181_processed_data.csv": "results/csv_data/ID_0x0181_data.csv",
        "0x0182_processed_data.csv": "results/csv_data/ID_0x0182_data.csv",
        "0x0183_processed_data.csv": "results/csv_data/ID_0x0183_data.csv",
        "0x0184_processed_data.csv": "results/csv_data/ID_0x0184_data.csv",
        "0x0185_processed_data.csv": "results/csv_data/ID_0x0185_data.csv",
        "0x0186_processed_data.csv": "results/csv_data/ID_0x0186_data.csv",
        
        # 图表文件
        "0x0181_data_plot.png": "results/charts/ID_0x0181_chart.png",
        "0x0182_data_plot.png": "results/charts/ID_0x0182_chart.png",
        "0x0183_data_plot.png": "results/charts/ID_0x0183_chart.png",
        "0x0184_data_plot.png": "results/charts/ID_0x0184_chart.png",
        "0x0185_data_plot.png": "results/charts/ID_0x0185_chart.png",
        "0x0186_data_plot.png": "results/charts/ID_0x0186_chart.png",
    }
    
    print(f"正在创建Windows兼容的压缩包: {package_name}")
    
    # 复制文件到临时目录
    for original_file, new_path in file_mappings.items():
        if os.path.exists(original_file):
            dest_path = os.path.join(temp_dir, new_path)
            shutil.copy2(original_file, dest_path)
            print(f"✓ 复制: {original_file} -> {new_path}")
        else:
            print(f"✗ 文件不存在: {original_file}")
    
    # 创建英文版README
    readme_content = """# CAN Bus Data Processing Project

## Project Overview
This project processes CAN bus data from Excel files, extracts data for specified IDs, converts the last 4 bytes to signed 32-bit integers, and generates visualization charts.

## File Structure

### Original Data
- `original_data/can_data_0903_0003.xlsx` - Original CAN bus data Excel file

### Processing Scripts
- `scripts/parse_xlsx_xml.py` - Script for processing single ID (0x0181)
- `scripts/process_multiple_ids.py` - Generic script for processing multiple IDs
- `scripts/convert_txt_to_csv.py` - Utility script for converting TXT to CSV format

### Processed Data Files (CSV format)
- `results/csv_data/ID_0x0181_data.csv` - Processed data for ID 0x0181
- `results/csv_data/ID_0x0182_data.csv` - Processed data for ID 0x0182
- `results/csv_data/ID_0x0183_data.csv` - Processed data for ID 0x0183
- `results/csv_data/ID_0x0184_data.csv` - Processed data for ID 0x0184
- `results/csv_data/ID_0x0185_data.csv` - Processed data for ID 0x0185
- `results/csv_data/ID_0x0186_data.csv` - Processed data for ID 0x0186

### Visualization Charts
- `results/charts/ID_0x0181_chart.png` - Line chart for ID 0x0181
- `results/charts/ID_0x0182_chart.png` - Line chart for ID 0x0182
- `results/charts/ID_0x0183_chart.png` - Line chart for ID 0x0183
- `results/charts/ID_0x0184_chart.png` - Line chart for ID 0x0184
- `results/charts/ID_0x0185_chart.png` - Line chart for ID 0x0185
- `results/charts/ID_0x0186_chart.png` - Line chart for ID 0x0186

## Data Processing Method

### Processing Flow
1. **Data Extraction**: Extract data rows for specified CAN IDs from Excel file
2. **Data Parsing**: Parse hexadecimal byte sequences in data column
3. **Last 4 Bytes Extraction**: Extract the last 4 bytes from each data entry
4. **Byte Order Conversion**: Rearrange bytes in little-endian order
5. **Data Type Conversion**: Convert 32-bit hexadecimal to signed 32-bit integer
6. **Visualization**: Generate line charts showing data trends
7. **Data Export**: Save results in CSV format

### Technical Details
- **Byte Order**: Last 4 bytes arranged in little-endian order (LSB first)
- **Signed Conversion**: Use two's complement representation for signed 32-bit integers
- **Data Range**: Signed 32-bit integer range [-2,147,483,648 to 2,147,483,647]

## Processing Results Statistics

| CAN ID | Data Points | Average | Std Dev | Min Value | Max Value |
|--------|-------------|---------|---------|-----------|-----------|
| 0x0181 | 2907 | -143,408.98 | 3,288.08 | -152,887 | -137,875 |
| 0x0182 | 2908 | -132,524.16 | 3,287.94 | -141,997 | -127,095 |
| 0x0183 | 2907 | -112,740.98 | 3,268.37 | -122,193 | -107,079 |
| 0x0184 | 2908 | -119,767.20 | 3,193.06 | -126,318 | -110,545 |
| 0x0185 | 2908 | -73,306.17 | 3,257.17 | -82,751 | -67,633 |
| 0x0186 | 2908 | -80,374.17 | 3,257.47 | -89,819 | -74,700 |

## Usage

### Requirements
```bash
pip install pandas numpy matplotlib openpyxl
```

### Running Scripts
1. **Process single ID (0x0181)**:
   ```bash
   python scripts/parse_xlsx_xml.py
   ```

2. **Process multiple IDs (0x0182-0x0186)**:
   ```bash
   python scripts/process_multiple_ids.py
   ```

3. **Convert file format**:
   ```bash
   python scripts/convert_txt_to_csv.py
   ```

### Custom Processing
To process other CAN IDs, modify the `target_ids` list in `process_multiple_ids.py`:
```python
target_ids = ['0x0187', '0x0188', '0x0189']  # Add your desired IDs
```

## CSV File Format
Each CSV file contains two columns:
- `Time`: Data collection timestamp
- `Signed_32bit_Value`: Converted numerical value

Example:
```csv
Time,Signed_32bit_Value
09:04:10.454,-138799
09:04:10.454,-138731
09:04:10.454,-138690
```

## Notes
1. Original Excel file has format issues, XML parsing method bypasses openpyxl limitations
2. Data conversion uses little-endian byte order, suitable for most embedded systems
3. All negative values indicate these may be sensor readings or control signals
4. Charts include statistical information (mean, standard deviation) for data analysis
"""
    
    # 保存英文README
    with open(os.path.join(temp_dir, "README.md"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ 创建: README.md")
    
    # 创建项目结构说明
    structure_content = """CAN Bus Data Processing Project Structure

Project/
├── README.md                           # Project documentation
├── original_data/
│   └── can_data_0903_0003.xlsx        # Original CAN bus data
├── scripts/
│   ├── parse_xlsx_xml.py              # Single ID processing script
│   ├── process_multiple_ids.py        # Multiple ID processing script
│   └── convert_txt_to_csv.py          # Format conversion utility
├── results/
│   ├── csv_data/
│   │   ├── ID_0x0181_data.csv         # ID 0x0181 data
│   │   ├── ID_0x0182_data.csv         # ID 0x0182 data
│   │   ├── ID_0x0183_data.csv         # ID 0x0183 data
│   │   ├── ID_0x0184_data.csv         # ID 0x0184 data
│   │   ├── ID_0x0185_data.csv         # ID 0x0185 data
│   │   └── ID_0x0186_data.csv         # ID 0x0186 data
│   └── charts/
│       ├── ID_0x0181_chart.png        # ID 0x0181 line chart
│       ├── ID_0x0182_chart.png        # ID 0x0182 line chart
│       ├── ID_0x0183_chart.png        # ID 0x0183 line chart
│       ├── ID_0x0184_chart.png        # ID 0x0184 line chart
│       ├── ID_0x0185_chart.png        # ID 0x0185 line chart
│       └── ID_0x0186_chart.png        # ID 0x0186 line chart

Usage Instructions:
1. Read README.md for detailed project information
2. Install dependencies to run scripts: pip install pandas numpy matplotlib openpyxl
3. CSV files can be opened directly with Excel or other data analysis tools
4. PNG chart files can be viewed directly to observe data trends
"""
    
    with open(os.path.join(temp_dir, "Project_Structure.txt"), 'w', encoding='utf-8') as f:
        f.write(structure_content)
    print("✓ 创建: Project_Structure.txt")
    
    # 创建压缩包
    with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, temp_dir)
                zipf.write(file_path, arc_path)
                print(f"✓ 打包: {arc_path}")
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print(f"\n✅ 打包完成！")
    print(f"📦 压缩包名称: {package_name}")
    print(f"📁 压缩包大小: {os.path.getsize(package_name) / 1024 / 1024:.2f} MB")
    print(f"🌍 Windows兼容: 使用英文文件名和路径，避免乱码问题")
    
    return package_name

if __name__ == "__main__":
    package_name = create_windows_compatible_package()
    print(f"\n🎉 Windows兼容压缩包已创建: {package_name}")
