import os
import zipfile
from datetime import datetime
import shutil

def create_final_windows_package():
    """创建最终的Windows兼容压缩包，使用正确处理的数据"""
    
    # 获取当前时间作为包名的一部分
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"CAN_Data_Processing_Final_{timestamp}.zip"
    
    # 创建临时文件夹结构（使用英文名）
    temp_dir = "temp_final_package"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    
    # 创建文件夹结构
    folders = {
        "original_data": "原始数据",
        "scripts": "处理脚本", 
        "results/csv_data": "处理结果/CSV数据",
        "results/charts": "处理结果/图表"
    }
    
    for folder in folders.keys():
        os.makedirs(os.path.join(temp_dir, folder), exist_ok=True)
    
    # 文件映射（使用修正版的文件）
    file_mappings = {
        # 原始数据
        "0903_0003.xlsx": "original_data/can_data_0903_0003.xlsx",
        
        # 脚本文件（使用最终版本）
        "final_process_all_ids.py": "scripts/process_can_data.py",
        
        # CSV数据文件（使用修正版）
        "0x0181_corrected_processed_data.csv": "results/csv_data/ID_0x0181_data.csv",
        "0x0182_corrected_processed_data.csv": "results/csv_data/ID_0x0182_data.csv",
        "0x0183_corrected_processed_data.csv": "results/csv_data/ID_0x0183_data.csv",
        "0x0184_corrected_processed_data.csv": "results/csv_data/ID_0x0184_data.csv",
        "0x0185_corrected_processed_data.csv": "results/csv_data/ID_0x0185_data.csv",
        "0x0186_corrected_processed_data.csv": "results/csv_data/ID_0x0186_data.csv",
        
        # 图表文件（使用修正版）
        "0x0181_corrected_data_plot.png": "results/charts/ID_0x0181_chart.png",
        "0x0182_corrected_data_plot.png": "results/charts/ID_0x0182_chart.png",
        "0x0183_corrected_data_plot.png": "results/charts/ID_0x0183_chart.png",
        "0x0184_corrected_data_plot.png": "results/charts/ID_0x0184_chart.png",
        "0x0185_corrected_data_plot.png": "results/charts/ID_0x0185_chart.png",
        "0x0186_corrected_data_plot.png": "results/charts/ID_0x0186_chart.png",
    }
    
    print(f"正在创建最终Windows兼容的压缩包: {package_name}")
    
    # 复制文件到临时目录
    for original_file, new_path in file_mappings.items():
        if os.path.exists(original_file):
            dest_path = os.path.join(temp_dir, new_path)
            shutil.copy2(original_file, dest_path)
            print(f"✓ 复制: {original_file} -> {new_path}")
        else:
            print(f"✗ 文件不存在: {original_file}")
    
    # 创建英文版README
    readme_content = """# CAN Bus Data Processing Project

## Project Overview
This project processes CAN bus data from Excel files, extracts data for specified IDs, converts the last 4 bytes to signed 32-bit integers, and generates visualization charts.

## File Structure

### Original Data
- `original_data/can_data_0903_0003.xlsx` - Original CAN bus data Excel file

### Processing Scripts
- `scripts/process_can_data.py` - Main script for processing CAN data

### Processed Data Files (CSV format)
- `results/csv_data/ID_0x0181_data.csv` - Processed data for ID 0x0181
- `results/csv_data/ID_0x0182_data.csv` - Processed data for ID 0x0182
- `results/csv_data/ID_0x0183_data.csv` - Processed data for ID 0x0183
- `results/csv_data/ID_0x0184_data.csv` - Processed data for ID 0x0184
- `results/csv_data/ID_0x0185_data.csv` - Processed data for ID 0x0185
- `results/csv_data/ID_0x0186_data.csv` - Processed data for ID 0x0186

### Visualization Charts
- `results/charts/ID_0x0181_chart.png` - Line chart for ID 0x0181
- `results/charts/ID_0x0182_chart.png` - Line chart for ID 0x0182
- `results/charts/ID_0x0183_chart.png` - Line chart for ID 0x0183
- `results/charts/ID_0x0184_chart.png` - Line chart for ID 0x0184
- `results/charts/ID_0x0185_chart.png` - Line chart for ID 0x0185
- `results/charts/ID_0x0186_chart.png` - Line chart for ID 0x0186

## Data Processing Method

### Processing Flow
1. **Data Extraction**: Extract data rows for specified CAN IDs from Excel file
2. **Data Parsing**: Parse hexadecimal byte sequences in data column
3. **Last 4 Bytes Extraction**: Extract the last 4 bytes from each data entry
4. **Byte Order Conversion**: Rearrange bytes with first byte as LSB, last byte as MSB
5. **Data Type Conversion**: Convert 32-bit hexadecimal to signed 32-bit integer
6. **Visualization**: Generate line charts showing data trends
7. **Data Export**: Save results in CSV format

### Technical Details
- **Byte Order**: Last 4 bytes with first byte as LSB (Least Significant Byte)
- **Example**: `32 12 07 7F CC FD FF` → Extract `7F CC FD FF` → Rearrange to `FF FD CC 7F`
- **Signed Conversion**: Use two's complement representation for signed 32-bit integers
- **Data Range**: Signed 32-bit integer range [-2,147,483,648 to 2,147,483,647]

## Processing Results Statistics

| CAN ID | Data Points | Average | Std Dev | Min Value | Max Value |
|--------|-------------|---------|---------|-----------|-----------|
| 0x0181 | 2907 | -143,408.98 | 3,288.08 | -152,887 | -137,875 |
| 0x0182 | 2908 | -132,524.16 | 3,287.94 | -141,997 | -127,095 |
| 0x0183 | 2907 | -112,740.98 | 3,268.37 | -122,193 | -107,079 |
| 0x0184 | 2908 | -119,767.20 | 3,193.06 | -126,318 | -110,545 |
| 0x0185 | 2908 | -73,306.17 | 3,257.17 | -82,751 | -67,633 |
| 0x0186 | 2908 | -80,374.17 | 3,257.47 | -89,819 | -74,700 |

## Usage

### Requirements
```bash
pip install pandas numpy matplotlib openpyxl
```

### Running Scripts
```bash
python scripts/process_can_data.py
```

### Custom Processing
To process other CAN IDs, modify the `target_ids` list in the script:
```python
target_ids = ['0x0187', '0x0188', '0x0189']  # Add your desired IDs
```

## CSV File Format
Each CSV file contains two columns:
- `Time`: Data collection timestamp
- `Signed_32bit_Value`: Converted numerical value

Example:
```csv
Time,Signed_32bit_Value
09:04:10.454,-138799
09:04:10.454,-138731
09:04:10.454,-138690
```

## Notes
1. Original Excel file has format issues, XML parsing method bypasses openpyxl limitations
2. Data conversion uses specific byte ordering with first byte as LSB
3. All negative values indicate these may be sensor readings or control signals
4. Charts include statistical information (mean, standard deviation) for data analysis
"""
    
    # 保存英文README
    with open(os.path.join(temp_dir, "README.md"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ 创建: README.md")
    
    # 创建项目结构说明
    structure_content = """CAN Bus Data Processing Project Structure

Project/
├── README.md                           # Project documentation
├── original_data/
│   └── can_data_0903_0003.xlsx        # Original CAN bus data
├── scripts/
│   └── process_can_data.py            # Main processing script
├── results/
│   ├── csv_data/
│   │   ├── ID_0x0181_data.csv         # ID 0x0181 processed data
│   │   ├── ID_0x0182_data.csv         # ID 0x0182 processed data
│   │   ├── ID_0x0183_data.csv         # ID 0x0183 processed data
│   │   ├── ID_0x0184_data.csv         # ID 0x0184 processed data
│   │   ├── ID_0x0185_data.csv         # ID 0x0185 processed data
│   │   └── ID_0x0186_data.csv         # ID 0x0186 processed data
│   └── charts/
│       ├── ID_0x0181_chart.png        # ID 0x0181 line chart
│       ├── ID_0x0182_chart.png        # ID 0x0182 line chart
│       ├── ID_0x0183_chart.png        # ID 0x0183 line chart
│       ├── ID_0x0184_chart.png        # ID 0x0184 line chart
│       ├── ID_0x0185_chart.png        # ID 0x0185 line chart
│       └── ID_0x0186_chart.png        # ID 0x0186 line chart

Data Processing Logic:
1. Extract last 4 bytes from CAN data
2. First byte is LSB (Least Significant Byte), last byte is MSB
3. Convert to signed 32-bit integer using two's complement

Usage Instructions:
1. Read README.md for detailed project information
2. Install dependencies: pip install pandas numpy matplotlib openpyxl
3. Run the processing script to analyze CAN data
4. CSV files can be opened with Excel or other data analysis tools
5. PNG chart files show data trends and statistical information
"""
    
    with open(os.path.join(temp_dir, "Project_Structure.txt"), 'w', encoding='utf-8') as f:
        f.write(structure_content)
    print("✓ 创建: Project_Structure.txt")
    
    # 创建压缩包
    with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, temp_dir)
                zipf.write(file_path, arc_path)
                print(f"✓ 打包: {arc_path}")
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print(f"\n✅ 最终打包完成！")
    print(f"📦 压缩包名称: {package_name}")
    print(f"📁 压缩包大小: {os.path.getsize(package_name) / 1024 / 1024:.2f} MB")
    print(f"🌍 Windows兼容: 使用英文文件名和路径，避免乱码问题")
    print(f"✨ 数据处理: 使用正确的字节序处理逻辑")
    
    return package_name

if __name__ == "__main__":
    package_name = create_final_windows_package()
    print(f"\n🎉 最终交付版压缩包已创建: {package_name}")
