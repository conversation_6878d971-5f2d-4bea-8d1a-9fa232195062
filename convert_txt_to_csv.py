import os

# 将已有的0x0181 TXT文件转换为CSV
if os.path.exists('0x0181_processed_data.txt'):
    with open('0x0181_processed_data.txt', 'r') as f:
        content = f.read()
    
    with open('0x0181_processed_data.csv', 'w') as f:
        f.write(content)
    
    print("已将0x0181_processed_data.txt转换为0x0181_processed_data.csv")
    
    # 删除原TXT文件
    os.remove('0x0181_processed_data.txt')
    print("已删除原TXT文件")
else:
    print("未找到0x0181_processed_data.txt文件")

# 删除其他可能存在的TXT文件
txt_files = [f for f in os.listdir('.') if f.endswith('_processed_data.txt')]
for txt_file in txt_files:
    csv_file = txt_file.replace('.txt', '.csv')
    if not os.path.exists(csv_file):
        with open(txt_file, 'r') as f:
            content = f.read()
        with open(csv_file, 'w') as f:
            f.write(content)
        print(f"已将{txt_file}转换为{csv_file}")
    os.remove(txt_file)
    print(f"已删除{txt_file}")

print("转换完成！")
