import zipfile
import xml.etree.ElementTree as ET
import re
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def extract_last_four_bytes_to_signed_int32(data_str):
    """从数据字符串中提取后四位字节并转换为有符号32位整数"""
    try:
        if not data_str:
            return None
        # 移除 'x|' 前缀和多余空格
        data_str = str(data_str).replace('x|', '').strip()
        # 分割十六进制数据
        hex_bytes = data_str.split()
        
        if len(hex_bytes) >= 4:
            # 取后四个字节，组合成32位十六进制（小端序）
            last_four = hex_bytes[-4:]
            hex_value = ''.join(reversed(last_four))
            
            # 转换为无符号整数
            unsigned_int = int(hex_value, 16)
            # 转换为有符号32位整数
            if unsigned_int >= 2**31:
                signed_int = unsigned_int - 2**32
            else:
                signed_int = unsigned_int
            return signed_int
        return None
    except Exception as e:
        return None

def process_can_id(target_id):
    """处理指定ID的CAN数据"""
    file_path = '0903_0003.xlsx'
    print(f"正在处理ID {target_id} 的数据...")
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            try:
                with zip_file.open('xl/sharedStrings.xml') as f:
                    tree = ET.parse(f)
                    root = tree.getroot()
                    for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                        t = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                        if t is not None:
                            shared_strings.append(t.text)
                        else:
                            shared_strings.append('')
            except:
                print("无法读取共享字符串，将直接解析单元格值")
            
            # 读取工作表数据
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                tree = ET.parse(f)
                root = tree.getroot()
                
                # 存储数据
                data_values = []
                timestamps = []
                row_count = 0
                
                # 解析每一行
                for row in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row'):
                    row_count += 1
                    if row_count == 1:  # 跳过标题行
                        continue
                    
                    if row_count % 10000 == 0:
                        print(f"已处理 {row_count} 行，找到 {len(data_values)} 个{target_id}数据点")
                    
                    cells = row.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c')
                    
                    # 提取需要的列数据
                    id_value = None
                    data_value = None
                    time_value = None
                    
                    for cell in cells:
                        r = cell.get('r')  # 单元格引用，如A1, B1等
                        if r:
                            col_letter = re.match(r'([A-Z]+)', r).group(1)
                            
                            # 获取单元格值
                            v = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                            if v is not None:
                                cell_value = v.text
                                
                                # 如果是共享字符串类型
                                t = cell.get('t')
                                if t == 's' and cell_value and shared_strings:
                                    try:
                                        cell_value = shared_strings[int(cell_value)]
                                    except:
                                        pass
                                
                                # 根据列位置提取数据
                                if col_letter == 'F':  # ID号列
                                    id_value = cell_value
                                elif col_letter == 'J':  # 数据列
                                    data_value = cell_value
                                elif col_letter == 'B':  # 时间列
                                    time_value = cell_value
                    
                    # 处理目标ID的数据
                    if id_value == target_id and data_value:
                        signed_value = extract_last_four_bytes_to_signed_int32(data_value)
                        if signed_value is not None:
                            data_values.append(signed_value)
                            timestamps.append(str(time_value) if time_value else f"row_{row_count}")
        
        return data_values, timestamps
    
    except Exception as e:
        print(f"处理数据时出错: {e}")
        return [], []

def plot_and_save_data(target_id, data_values, timestamps):
    """绘制图表并保存数据"""
    if len(data_values) == 0:
        print(f"未找到ID为{target_id}的数据")
        return
    
    print(f"\n总共找到 {len(data_values)} 个ID为{target_id}的数据点")
    print(f"前10个数据值: {data_values[:10]}")
    print(f"数据范围: {min(data_values)} 到 {max(data_values)}")
    
    # 绘制折线图
    plt.figure(figsize=(15, 8))
    plt.plot(range(len(data_values)), data_values, 'b-', linewidth=0.8, alpha=0.8)
    plt.title(f'ID {target_id} 数据的后四位转换为有符号32位整数的折线图', fontsize=14)
    plt.xlabel('数据点序号', fontsize=12)
    plt.ylabel('有符号32位整数值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    mean_val = np.mean(data_values)
    std_val = np.std(data_values)
    plt.axhline(y=mean_val, color='r', linestyle='--', alpha=0.7, label=f'平均值: {mean_val:.2f}')
    plt.axhline(y=mean_val + std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值+标准差: {mean_val + std_val:.2f}')
    plt.axhline(y=mean_val - std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值-标准差: {mean_val - std_val:.2f}')
    
    plt.legend()
    plt.tight_layout()
    
    # 保存图表
    plot_filename = f'{target_id}_data_plot.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n统计信息:")
    print(f"平均值: {mean_val:.2f}")
    print(f"标准差: {std_val:.2f}")
    print(f"最小值: {min(data_values)}")
    print(f"最大值: {max(data_values)}")
    print(f"图表已保存为 '{plot_filename}'")
    
    # 保存数据到CSV文件
    data_filename = f'{target_id}_processed_data.csv'
    with open(data_filename, 'w') as f:
        f.write("时间,有符号32位整数值\n")
        for time, value in zip(timestamps, data_values):
            f.write(f"{time},{value}\n")
    print(f"处理后的数据已保存为 '{data_filename}'")

# 主程序
if __name__ == "__main__":
    # 要处理的ID列表
    target_ids = ['0x0182', '0x0183', '0x0184', '0x0185', '0x0186']
    
    print("开始处理多个CAN ID的数据...")
    
    for target_id in target_ids:
        print(f"\n{'='*50}")
        data_values, timestamps = process_can_id(target_id)
        plot_and_save_data(target_id, data_values, timestamps)
    
    print(f"\n{'='*50}")
    print("所有ID处理完成！")
