import openpyxl
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 直接使用openpyxl读取Excel文件
file_path = '0903_0003.xlsx'
print("正在使用openpyxl读取Excel文件...")

try:
    # 打开工作簿，使用data_only=True来获取计算值而不是公式
    workbook = openpyxl.load_workbook(file_path, data_only=True, read_only=True)
    print(f"工作表名称: {workbook.sheetnames}")
    
    # 选择第一个工作表
    worksheet = workbook.active
    print(f"活动工作表: {worksheet.title}")
    
    # 获取数据范围
    max_row = worksheet.max_row
    max_col = worksheet.max_column
    print(f"数据范围: {max_row} 行 x {max_col} 列")
    
    # 读取前几行数据来了解结构
    print("\n前10行数据:")
    for row in range(1, min(11, max_row + 1)):
        row_data = []
        for col in range(1, min(max_col + 1, 20)):  # 只显示前20列
            cell_value = worksheet.cell(row=row, column=col).value
            row_data.append(str(cell_value) if cell_value is not None else "")
        print(f"行 {row}: {' | '.join(row_data)}")
    
    # 查找包含181的行
    print("\n查找包含'181'的行:")
    found_rows = []
    for row in range(1, min(max_row + 1, 1000)):  # 检查前1000行
        for col in range(1, max_col + 1):
            cell_value = worksheet.cell(row=row, column=col).value
            if cell_value is not None and '181' in str(cell_value):
                row_data = []
                for c in range(1, max_col + 1):
                    val = worksheet.cell(row=row, column=c).value
                    row_data.append(str(val) if val is not None else "")
                print(f"行 {row}: {' | '.join(row_data)}")
                found_rows.append(row)
                break
        if len(found_rows) >= 10:  # 只显示前10个匹配的行
            break
    
    workbook.close()
    
except Exception as e:
    print(f"读取Excel文件时出错: {e}")
    import traceback
    traceback.print_exc()
