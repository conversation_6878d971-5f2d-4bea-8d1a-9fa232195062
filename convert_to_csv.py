import pandas as pd

# 尝试不同的方法读取Excel文件
file_path = '0903_0003.xlsx'

try:
    print("尝试方法1: 使用xlrd引擎...")
    df = pd.read_excel(file_path, engine='xlrd')
    print("成功!")
except Exception as e1:
    print(f"方法1失败: {e1}")
    
    try:
        print("尝试方法2: 跳过错误行...")
        # 尝试读取为CSV格式
        import subprocess
        result = subprocess.run(['libreoffice', '--headless', '--convert-to', 'csv', file_path], 
                              capture_output=True, text=True, cwd='/Users/<USER>/Desktop/数据处理')
        if result.returncode == 0:
            print("转换为CSV成功")
            df = pd.read_csv('0903_0003.csv')
        else:
            raise Exception("LibreOffice转换失败")
    except Exception as e2:
        print(f"方法2失败: {e2}")
        
        try:
            print("尝试方法3: 使用openpyxl但忽略格式...")
            # 手动读取并保存为CSV
            import openpyxl
            
            # 尝试不验证格式
            workbook = openpyxl.load_workbook(file_path, data_only=True, read_only=False)
            worksheet = workbook.active
            
            # 手动提取数据
            data = []
            for row in range(1, min(100, worksheet.max_row + 1)):  # 先试100行
                row_data = []
                for col in range(1, worksheet.max_column + 1):
                    try:
                        cell_value = worksheet.cell(row=row, column=col).value
                        row_data.append(cell_value)
                    except:
                        row_data.append(None)
                data.append(row_data)
            
            # 创建DataFrame
            df = pd.DataFrame(data[1:], columns=data[0])
            print("方法3成功!")
            
        except Exception as e3:
            print(f"方法3也失败: {e3}")
            print("尝试最后的方法：直接用Python处理...")
            
            # 最后的尝试：用xlwings或其他方法
            raise Exception("所有方法都失败了")

# 如果成功读取，保存为CSV
if 'df' in locals():
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print("前5行:")
    print(df.head())
    
    # 保存为CSV
    df.to_csv('can_data.csv', index=False, encoding='utf-8')
    print("已保存为 can_data.csv")
else:
    print("无法读取Excel文件")
