import openpyxl
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def extract_last_four_bytes(data_str):
    """从数据字符串中提取后四位字节并转换为有符号32位整数"""
    try:
        if not data_str:
            return None
        # 移除 'x|' 前缀和多余空格
        data_str = str(data_str).replace('x|', '').strip()
        # 分割十六进制数据
        hex_bytes = data_str.split()
        
        if len(hex_bytes) >= 4:
            # 取后四个字节，组合成32位十六进制（小端序）
            last_four = hex_bytes[-4:]
            hex_value = ''.join(reversed(last_four))
            
            # 转换为无符号整数
            unsigned_int = int(hex_value, 16)
            # 转换为有符号32位整数
            if unsigned_int >= 2**31:
                signed_int = unsigned_int - 2**32
            else:
                signed_int = unsigned_int
            return signed_int
        return None
    except Exception as e:
        print(f"处理数据时出错: {data_str}, 错误: {e}")
        return None

# 读取Excel文件
file_path = '0903_0003.xlsx'
print("正在处理CAN数据...")

try:
    workbook = openpyxl.load_workbook(file_path, data_only=True, read_only=True)
    worksheet = workbook.active
    
    # 存储0x0181的数据
    data_0x0181 = []
    timestamps = []
    
    max_row = worksheet.max_row
    print(f"总行数: {max_row}")
    
    # 批量读取数据
    print("正在批量读取数据...")
    rows_data = []
    for row in worksheet.iter_rows(min_row=2, max_row=max_row, min_col=1, max_col=10, values_only=True):
        rows_data.append(row)
    
    print(f"读取完成，开始处理...")
    
    # 处理数据
    for i, row in enumerate(rows_data):
        if i % 10000 == 0:
            print(f"已处理 {i} 行，找到 {len(data_0x0181)} 个0x0181数据点")
        
        id_value = row[5]  # ID号在第6列（索引5）
        data_value = row[9]  # 数据在第10列（索引9）
        time_value = row[1]  # 时间在第2列（索引1）
        
        if id_value == '0x0181' and data_value:
            # 提取后四位数据并转换
            signed_value = extract_last_four_bytes(data_value)
            if signed_value is not None:
                data_0x0181.append(signed_value)
                timestamps.append(str(time_value))
    
    workbook.close()
    
    print(f"\n总共找到 {len(data_0x0181)} 个ID为0x0181的数据点")
    
    if len(data_0x0181) > 0:
        print(f"前10个数据值: {data_0x0181[:10]}")
        print(f"数据范围: {min(data_0x0181)} 到 {max(data_0x0181)}")
        
        # 绘制折线图
        plt.figure(figsize=(15, 8))
        plt.plot(range(len(data_0x0181)), data_0x0181, 'b-', linewidth=0.8, alpha=0.8)
        plt.title('ID 0x0181 数据的后四位转换为有符号32位整数的折线图', fontsize=14)
        plt.xlabel('数据点序号', fontsize=12)
        plt.ylabel('有符号32位整数值', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_val = np.mean(data_0x0181)
        std_val = np.std(data_0x0181)
        plt.axhline(y=mean_val, color='r', linestyle='--', alpha=0.7, label=f'平均值: {mean_val:.2f}')
        plt.axhline(y=mean_val + std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值+标准差: {mean_val + std_val:.2f}')
        plt.axhline(y=mean_val - std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值-标准差: {mean_val - std_val:.2f}')
        
        plt.legend()
        plt.tight_layout()
        plt.savefig('0x0181_data_plot.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n统计信息:")
        print(f"平均值: {mean_val:.2f}")
        print(f"标准差: {std_val:.2f}")
        print(f"最小值: {min(data_0x0181)}")
        print(f"最大值: {max(data_0x0181)}")
        print(f"图表已保存为 '0x0181_data_plot.png'")
        
        # 保存数据到文本文件
        with open('0x0181_processed_data.txt', 'w') as f:
            f.write("时间,有符号32位整数值\n")
            for i, (time, value) in enumerate(zip(timestamps, data_0x0181)):
                f.write(f"{time},{value}\n")
        print(f"处理后的数据已保存为 '0x0181_processed_data.txt'")
        
    else:
        print("未找到ID为0x0181的数据")

except Exception as e:
    print(f"处理数据时出错: {e}")
    import traceback
    traceback.print_exc()
