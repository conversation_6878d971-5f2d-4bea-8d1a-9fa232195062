# CAN总线数据处理项目

## 项目概述
本项目用于处理CAN总线数据，从Excel文件中提取指定ID的数据，将数据的后四位字节转换为有符号32位整数，并生成可视化图表。

## 文件结构

### 原始数据
- `0903_0003.xlsx` - 原始CAN总线数据Excel文件

### 处理脚本
- `parse_xlsx_xml.py` - 处理单个ID (0x0181) 的脚本
- `process_multiple_ids.py` - 处理多个ID的通用脚本
- `convert_txt_to_csv.py` - 将TXT文件转换为CSV格式的工具脚本

### 处理后的数据文件 (CSV格式)
- `0x0181_processed_data.csv` - ID 0x0181 的处理结果
- `0x0182_processed_data.csv` - ID 0x0182 的处理结果
- `0x0183_processed_data.csv` - ID 0x0183 的处理结果
- `0x0184_processed_data.csv` - ID 0x0184 的处理结果
- `0x0185_processed_data.csv` - ID 0x0185 的处理结果
- `0x0186_processed_data.csv` - ID 0x0186 的处理结果

### 可视化图表
- `0x0181_data_plot.png` - ID 0x0181 的折线图
- `0x0182_data_plot.png` - ID 0x0182 的折线图
- `0x0183_data_plot.png` - ID 0x0183 的折线图
- `0x0184_data_plot.png` - ID 0x0184 的折线图
- `0x0185_data_plot.png` - ID 0x0185 的折线图
- `0x0186_data_plot.png` - ID 0x0186 的折线图

## 数据处理方法

### 处理流程
1. **数据提取**: 从Excel文件中提取指定CAN ID的数据行
2. **数据解析**: 解析数据列中的十六进制字节序列
3. **后四位提取**: 提取每条数据的后四个字节
4. **字节序转换**: 按小端序(Little Endian)重新排列字节
5. **数据类型转换**: 将32位十六进制数转换为有符号32位整数
6. **可视化**: 生成折线图显示数据趋势
7. **数据保存**: 将结果保存为CSV格式

### 技术细节
- **字节序处理**: 后四位字节按小端序排列 (最低位字节在前)
- **有符号转换**: 使用二进制补码表示法转换为有符号32位整数
- **数据范围**: 有符号32位整数范围 [-2,147,483,648 到 2,147,483,647]

## 处理结果统计

| CAN ID | 数据点数量 | 平均值 | 标准差 | 最小值 | 最大值 |
|--------|------------|--------|--------|--------|--------|
| 0x0181 | 2907 | -143,408.98 | 3,288.08 | -152,887 | -137,875 |
| 0x0182 | 2908 | -132,524.16 | 3,287.94 | -141,997 | -127,095 |
| 0x0183 | 2907 | -112,740.98 | 3,268.37 | -122,193 | -107,079 |
| 0x0184 | 2908 | -119,767.20 | 3,193.06 | -126,318 | -110,545 |
| 0x0185 | 2908 | -73,306.17 | 3,257.17 | -82,751 | -67,633 |
| 0x0186 | 2908 | -80,374.17 | 3,257.47 | -89,819 | -74,700 |

## 使用方法

### 环境要求
```bash
pip install pandas numpy matplotlib openpyxl
```

### 运行脚本
1. **处理单个ID (0x0181)**:
   ```bash
   python parse_xlsx_xml.py
   ```

2. **处理多个ID (0x0182-0x0186)**:
   ```bash
   python process_multiple_ids.py
   ```

3. **转换文件格式**:
   ```bash
   python convert_txt_to_csv.py
   ```

### 自定义处理
要处理其他CAN ID，可以修改 `process_multiple_ids.py` 中的 `target_ids` 列表：
```python
target_ids = ['0x0187', '0x0188', '0x0189']  # 添加你需要的ID
```

## CSV文件格式
每个CSV文件包含两列：
- `时间`: 数据采集的时间戳
- `有符号32位整数值`: 转换后的数值

示例：
```csv
时间,有符号32位整数值
09:04:10.454,-138799
09:04:10.454,-138731
09:04:10.454,-138690
```

## 注意事项
1. 原始Excel文件存在格式问题，使用XML解析方式绕过了openpyxl的限制
2. 数据转换采用小端序，适用于大多数嵌入式系统
3. 所有负值表明这些可能是传感器读数或控制信号
4. 图表中包含统计信息（平均值、标准差）便于数据分析
