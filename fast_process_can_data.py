import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import re

def extract_last_four_bytes_vectorized(data_series):
    """向量化处理数据字符串，提取后四位字节"""
    def process_single(data_str):
        try:
            if pd.isna(data_str):
                return None
            # 移除 'x|' 前缀和多余空格
            data_str = str(data_str).replace('x|', '').strip()
            # 分割十六进制数据
            hex_bytes = data_str.split()
            
            if len(hex_bytes) >= 4:
                # 取后四个字节，组合成32位十六进制（小端序）
                last_four = hex_bytes[-4:]
                hex_value = ''.join(reversed(last_four))
                return hex_value
            return None
        except:
            return None
    
    return data_series.apply(process_single)

def hex_to_signed_int32_vectorized(hex_series):
    """向量化将十六进制转换为有符号32位整数"""
    def convert_single(hex_str):
        try:
            if pd.isna(hex_str) or hex_str is None:
                return None
            # 转换为无符号整数
            unsigned_int = int(hex_str, 16)
            # 转换为有符号32位整数
            if unsigned_int >= 2**31:
                signed_int = unsigned_int - 2**32
            else:
                signed_int = unsigned_int
            return signed_int
        except:
            return None
    
    return hex_series.apply(convert_single)

# 读取Excel文件
file_path = '0903_0003.xlsx'
print("正在快速读取Excel文件...")

try:
    # 使用pandas直接读取整个文件
    print("正在读取整个Excel文件...")
    df = pd.read_excel(file_path, engine='openpyxl')

    print(f"读取完成，共 {len(df)} 行数据")
    print(f"列名: {list(df.columns)}")

    # 筛选ID号为0x0181的数据
    mask_0x0181 = df.iloc[:, 5] == '0x0181'  # ID号在第6列（索引5）
    data_0x0181 = df[mask_0x0181].copy()

    print(f"找到 {len(data_0x0181)} 个ID为0x0181的数据点")

    if len(data_0x0181) > 0:
        # 提取后四位数据
        print("正在提取后四位数据...")
        data_0x0181['后四位hex'] = extract_last_four_bytes_vectorized(data_0x0181.iloc[:, 9])  # 数据在第10列（索引9）

        # 转换为有符号32位整数
        print("正在转换为有符号32位整数...")
        data_0x0181['signed_int32'] = hex_to_signed_int32_vectorized(data_0x0181['后四位hex'])

        # 过滤掉转换失败的数据
        final_data = data_0x0181.dropna(subset=['signed_int32']).copy()
        final_data = final_data[[df.columns[1], 'signed_int32']]  # 时间和转换后的数据
        final_data.columns = ['系统时间', 'signed_int32']

    if len(final_data) > 0:
        print(f"\n总共找到 {len(final_data)} 个有效的ID为0x0181的数据点")
        print(f"前10个数据值: {final_data['signed_int32'].head(10).tolist()}")
        print(f"数据范围: {final_data['signed_int32'].min()} 到 {final_data['signed_int32'].max()}")
        
        # 绘制折线图
        plt.figure(figsize=(15, 8))
        plt.plot(range(len(final_data)), final_data['signed_int32'], 'b-', linewidth=0.8, alpha=0.8)
        plt.title('ID 0x0181 数据的后四位转换为有符号32位整数的折线图', fontsize=14)
        plt.xlabel('数据点序号', fontsize=12)
        plt.ylabel('有符号32位整数值', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_val = final_data['signed_int32'].mean()
        std_val = final_data['signed_int32'].std()
        plt.axhline(y=mean_val, color='r', linestyle='--', alpha=0.7, label=f'平均值: {mean_val:.2f}')
        plt.axhline(y=mean_val + std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值+标准差: {mean_val + std_val:.2f}')
        plt.axhline(y=mean_val - std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值-标准差: {mean_val - std_val:.2f}')
        
        plt.legend()
        plt.tight_layout()
        plt.savefig('0x0181_data_plot.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n统计信息:")
        print(f"平均值: {mean_val:.2f}")
        print(f"标准差: {std_val:.2f}")
        print(f"最小值: {final_data['signed_int32'].min()}")
        print(f"最大值: {final_data['signed_int32'].max()}")
        print(f"图表已保存为 '0x0181_data_plot.png'")
        
        # 保存处理后的数据
        final_data.to_csv('0x0181_processed_data.csv', index=False)
        print(f"处理后的数据已保存为 '0x0181_processed_data.csv'")
        
    else:
        print("未找到ID为0x0181的数据")

except Exception as e:
    print(f"处理数据时出错: {e}")
    import traceback
    traceback.print_exc()
