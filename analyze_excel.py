import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import struct

# 读取Excel文件
file_path = '0903_0003.xlsx'
print("正在读取Excel文件...")

try:
    # 直接使用openpyxl引擎读取Excel文件
    excel_file = pd.ExcelFile(file_path, engine='openpyxl')
    print(f"Excel文件包含的sheet: {excel_file.sheet_names}")

    # 读取第一个sheet，跳过可能有问题的行
    df = pd.read_excel(file_path, sheet_name=0, engine='openpyxl', header=None)

    print(f"\n数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print("\n前10行数据:")
    print(df.head(10))

    # 查看数据类型
    print("\n数据类型:")
    print(df.dtypes)

    # 查看所有数据的前几行，寻找包含ID信息的行
    print("\n查找包含ID或181的行:")
    for idx, row in df.iterrows():
        row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)])
        if '181' in row_str or 'ID' in row_str.upper() or 'id' in row_str:
            print(f"行 {idx}: {row_str}")
        if idx > 50:  # 只检查前50行
            break
    
    # 查看是否有ID列包含0x0181
    for col in df.columns:
        if 'ID' in str(col).upper() or 'id' in str(col):
            print(f"\n{col}列的唯一值:")
            unique_vals = df[col].unique()
            print(unique_vals[:20])  # 显示前20个唯一值
            
            # 检查是否包含0x0181相关的数据
            if any('181' in str(val) for val in unique_vals):
                print(f"在{col}列中找到包含'181'的值")
                matching_rows = df[df[col].astype(str).str.contains('181', na=False)]
                print(f"匹配的行数: {len(matching_rows)}")
                if len(matching_rows) > 0:
                    print("匹配的前几行:")
                    print(matching_rows.head())

except Exception as e:
    print(f"读取Excel文件时出错: {e}")
