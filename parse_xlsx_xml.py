import zipfile
import xml.etree.ElementTree as ET
import re
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def extract_last_four_bytes_to_signed_int32(data_str):
    """从数据字符串中提取后四位字节并转换为有符号32位整数"""
    try:
        if not data_str:
            return None
        # 移除 'x|' 前缀和多余空格
        data_str = str(data_str).replace('x|', '').strip()
        # 分割十六进制数据
        hex_bytes = data_str.split()
        
        if len(hex_bytes) >= 4:
            # 取后四个字节，组合成32位十六进制（小端序）
            last_four = hex_bytes[-4:]
            hex_value = ''.join(reversed(last_four))
            
            # 转换为无符号整数
            unsigned_int = int(hex_value, 16)
            # 转换为有符号32位整数
            if unsigned_int >= 2**31:
                signed_int = unsigned_int - 2**32
            else:
                signed_int = unsigned_int
            return signed_int
        return None
    except Exception as e:
        return None

# 解析xlsx文件
file_path = '0903_0003.xlsx'
print("正在解析xlsx文件的XML内容...")

try:
    with zipfile.ZipFile(file_path, 'r') as zip_file:
        # 读取共享字符串
        shared_strings = []
        try:
            with zip_file.open('xl/sharedStrings.xml') as f:
                tree = ET.parse(f)
                root = tree.getroot()
                for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                    t = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                    if t is not None:
                        shared_strings.append(t.text)
                    else:
                        shared_strings.append('')
        except:
            print("无法读取共享字符串，将直接解析单元格值")
        
        print(f"共享字符串数量: {len(shared_strings)}")
        
        # 读取工作表数据
        with zip_file.open('xl/worksheets/sheet1.xml') as f:
            tree = ET.parse(f)
            root = tree.getroot()
            
            # 存储数据
            data_0x0181 = []
            timestamps = []
            row_count = 0
            
            # 解析每一行
            for row in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row'):
                row_count += 1
                if row_count == 1:  # 跳过标题行
                    continue
                
                if row_count % 10000 == 0:
                    print(f"已处理 {row_count} 行，找到 {len(data_0x0181)} 个0x0181数据点")
                
                cells = row.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c')
                
                # 提取需要的列数据
                id_value = None
                data_value = None
                time_value = None
                
                for cell in cells:
                    r = cell.get('r')  # 单元格引用，如A1, B1等
                    if r:
                        col_letter = re.match(r'([A-Z]+)', r).group(1)
                        
                        # 获取单元格值
                        v = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                        if v is not None:
                            cell_value = v.text
                            
                            # 如果是共享字符串类型
                            t = cell.get('t')
                            if t == 's' and cell_value and shared_strings:
                                try:
                                    cell_value = shared_strings[int(cell_value)]
                                except:
                                    pass
                            
                            # 根据列位置提取数据
                            if col_letter == 'F':  # ID号列
                                id_value = cell_value
                            elif col_letter == 'J':  # 数据列
                                data_value = cell_value
                            elif col_letter == 'B':  # 时间列
                                time_value = cell_value
                
                # 处理0x0181的数据
                if id_value == '0x0181' and data_value:
                    signed_value = extract_last_four_bytes_to_signed_int32(data_value)
                    if signed_value is not None:
                        data_0x0181.append(signed_value)
                        timestamps.append(str(time_value) if time_value else f"row_{row_count}")
    
    print(f"\n总共找到 {len(data_0x0181)} 个ID为0x0181的数据点")
    
    if len(data_0x0181) > 0:
        print(f"前10个数据值: {data_0x0181[:10]}")
        print(f"数据范围: {min(data_0x0181)} 到 {max(data_0x0181)}")
        
        # 绘制折线图
        plt.figure(figsize=(15, 8))
        plt.plot(range(len(data_0x0181)), data_0x0181, 'b-', linewidth=0.8, alpha=0.8)
        plt.title('ID 0x0181 数据的后四位转换为有符号32位整数的折线图', fontsize=14)
        plt.xlabel('数据点序号', fontsize=12)
        plt.ylabel('有符号32位整数值', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_val = np.mean(data_0x0181)
        std_val = np.std(data_0x0181)
        plt.axhline(y=mean_val, color='r', linestyle='--', alpha=0.7, label=f'平均值: {mean_val:.2f}')
        plt.axhline(y=mean_val + std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值+标准差: {mean_val + std_val:.2f}')
        plt.axhline(y=mean_val - std_val, color='g', linestyle='--', alpha=0.7, label=f'平均值-标准差: {mean_val - std_val:.2f}')
        
        plt.legend()
        plt.tight_layout()
        plt.savefig('0x0181_data_plot.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n统计信息:")
        print(f"平均值: {mean_val:.2f}")
        print(f"标准差: {std_val:.2f}")
        print(f"最小值: {min(data_0x0181)}")
        print(f"最大值: {max(data_0x0181)}")
        print(f"图表已保存为 '0x0181_data_plot.png'")
        
        # 保存数据到文本文件
        with open('0x0181_processed_data.txt', 'w') as f:
            f.write("时间,有符号32位整数值\n")
            for time, value in zip(timestamps, data_0x0181):
                f.write(f"{time},{value}\n")
        print(f"处理后的数据已保存为 '0x0181_processed_data.txt'")
        
    else:
        print("未找到ID为0x0181的数据")

except Exception as e:
    print(f"处理数据时出错: {e}")
    import traceback
    traceback.print_exc()
